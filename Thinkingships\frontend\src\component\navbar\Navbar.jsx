import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useNavigate } from "react-router-dom";

function Navbar() {
    const navigate = useNavigate()
    const [isAuthorsDropdownOpen, setIsAuthorsDropdownOpen] = useState(false)
    const dropdownRef = useRef(null)

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setIsAuthorsDropdownOpen(false)
            }
        }
        document.addEventListener('mousedown', handleClickOutside)
        return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])
    return (
        <div className="navbar fixed top-0 left-0 right-0 z-50 bg-white shadow-lg py-2 sm:py-3 border-b border-gray-200 w-full">
            <div className="flex items-center justify-between w-full px-4 sm:px-6 lg:px-8">
                {/* Left Nav Links */}
                <ul className="flex gap-2 sm:gap-3 md:gap-6 lg:gap-8 text-xs sm:text-sm font-semibold text-gray-600">
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/romance")}
                        >
                            <span className="hidden md:inline">Romance</span>
                            <span className="md:hidden">Rom</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/fantasy")}
                        >
                            <span className="hidden md:inline">Fantasy</span>
                            <span className="md:hidden">Fan</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/mystery")}
                        >
                            <span className="hidden md:inline">Mystery</span>
                            <span className="md:hidden">Mys</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                    <li>
                        <a
                            className="hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 relative group px-1 sm:px-2 py-1 rounded-md hover:bg-blue-50"
                            onClick={()=>navigate("/all")}
                        >
                            <span className="hidden lg:inline">Browse All</span>
                            <span className="lg:hidden">All</span>
                            <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-[#4A99F8] transition-all duration-300 group-hover:w-full"></span>
                        </a>
                    </li>
                </ul>

                {/* Mobile Menu Button */}
                <div className="sm:hidden">
                    <button className="p-2 rounded-md text-gray-600 hover:text-[#4A99F8] hover:bg-blue-50 transition-all duration-300">
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                </div>

                {/* Center Logo / Branding */}
                <div className="flex items-center gap-1 sm:gap-2 text-[#4A99F8]">
                    <SquarePen
                        width={20}
                        height={20}
                        onClick={()=>navigate("/skrivee")}
                        className="sm:w-6 sm:h-6 cursor-pointer transition-all duration-300 hover:scale-110 hover:rotate-12 hover:text-[#3B82F6] drop-shadow-md"
                    />
                    <span
                        className="font-bold text-sm sm:text-base lg:text-lg tracking-wide cursor-pointer transition-all duration-300 hover:scale-105 hover:text-[#3B82F6] drop-shadow-sm"
                        onClick={()=>navigate("/skrivee")}
                    >
                        Skrivee
                    </span>
                </div>

                {/* Right side - Authors Dropdown */}
                <div className="relative" ref={dropdownRef}>
                    <button
                        onClick={() => setIsAuthorsDropdownOpen(!isAuthorsDropdownOpen)}
                        className="flex items-center gap-2 px-3 sm:px-4 py-2 text-xs sm:text-sm font-semibold text-gray-600 hover:text-[#4A99F8] transition-all duration-300 cursor-pointer hover:scale-105 rounded-md hover:bg-blue-50 border border-gray-200 hover:border-[#4A99F8]"
                    >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <span className="hidden sm:inline">Authors</span>
                        <span className="sm:hidden">Auth</span>
                        <svg className={`w-3 h-3 transition-transform duration-300 ${isAuthorsDropdownOpen ? 'rotate-180' : ''}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                        </svg>
                    </button>

                    {/* Dropdown Menu */}
                    {isAuthorsDropdownOpen && (
                        <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-2xl border border-gray-200 z-50 overflow-hidden">
                            {/* Header */}
                            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-100">
                                <h3 className="text-sm font-semibold text-gray-800 flex items-center gap-2">
                                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                                    </svg>
                                    Discover Authors
                                </h3>
                            </div>

                            {/* Menu Items */}
                            <div className="py-2">
                                <button
                                    onClick={() => {
                                        navigate("/authors")
                                        setIsAuthorsDropdownOpen(false)
                                    }}
                                    className="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors duration-200 flex items-center gap-3 group"
                                >
                                    <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium text-gray-800">All Authors</div>
                                        <div className="text-xs text-gray-500">Browse all writers</div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => {
                                        navigate("/authors?filter=trending")
                                        setIsAuthorsDropdownOpen(false)
                                    }}
                                    className="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors duration-200 flex items-center gap-3 group"
                                >
                                    <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium text-gray-800">Trending Authors</div>
                                        <div className="text-xs text-gray-500">Popular this week</div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => {
                                        navigate("/authors?filter=featured")
                                        setIsAuthorsDropdownOpen(false)
                                    }}
                                    className="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors duration-200 flex items-center gap-3 group"
                                >
                                    <div className="w-8 h-8 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium text-gray-800">Featured Authors</div>
                                        <div className="text-xs text-gray-500">Editor's choice</div>
                                    </div>
                                </button>

                                <button
                                    onClick={() => {
                                        navigate("/authors?filter=new")
                                        setIsAuthorsDropdownOpen(false)
                                    }}
                                    className="w-full px-4 py-3 text-left hover:bg-blue-50 transition-colors duration-200 flex items-center gap-3 group"
                                >
                                    <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                        </svg>
                                    </div>
                                    <div>
                                        <div className="text-sm font-medium text-gray-800">New Authors</div>
                                        <div className="text-xs text-gray-500">Fresh voices</div>
                                    </div>
                                </button>
                            </div>

                            {/* Footer */}
                            <div className="bg-gray-50 px-4 py-3 border-t border-gray-100">
                                <button
                                    onClick={() => {
                                        navigate("/authors/search")
                                        setIsAuthorsDropdownOpen(false)
                                    }}
                                    className="w-full text-center text-xs text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200 flex items-center justify-center gap-1"
                                >
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                                    </svg>
                                    Search Authors
                                </button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}

export default Navbar;
